name: CI/CD Pipeline

on:
  push:
    branches: [ main , test-deploy]
  pull_request:
    branches: [ main , test-deploy]

env:
  NODE_VERSION: '18'

jobs:
  # Integration test with Docker and tournament creation
  integration-test:
    name: Integration Test with Docker
    runs-on: ubuntu-latest
    # needs: docker-build

    env:
      # Ensure .env file exists for Docker setup
      CI: true

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Build TypeScript
      run: npm run build

    - name: Verify environment file exists
      run: |
        if [ ! -f ".env" ]; then
          echo "Creating .env from example..."
          cp .env.dev.example .env
        fi
        echo "Environment file ready"

    - name: Verify Docker Compose availability
      run: |
        echo "Checking Docker Compose availability..."
        if command -v docker-compose >/dev/null 2>&1; then
          echo "✅ docker-compose (legacy) is available"
          docker-compose --version
        fi
        if docker compose version >/dev/null 2>&1; then
          echo "✅ docker compose (plugin) is available"
          docker compose version
        fi

    - name: Start Docker development environment
      run: npm run dev:docker

    - name: Wait for services to be ready
      run: |
        echo "Waiting for services to start..."
        sleep 30

        # Verify services are running
        echo "Checking service status..."
        npm run dev:docker:status

    - name: Run tournament creation test
      run: npm run test:create-tournaments

    - name: Show logs on failure
      if: failure()
      run: |
        echo "=== API Server Logs ==="
        npm run dev:docker:logs:api || true
        echo "=== Database Logs ==="
        npm run dev:docker:logs:db || true
        echo "=== Consumer Logs ==="
        npm run dev:docker:logs:consumer || true

    - name: Stop Docker environment
      run: npm run dev:docker:stop
      if: always()
  # Basic CI job for build and test
  build-and-test:
    name: Build and Test
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Build TypeScript
      run: npm run build
      
    - name: Run tests (if available)
      run: |
        if [ -f "package.json" ] && grep -q '"test"' package.json; then
          npm test
        else
          echo "No tests found, skipping test step"
        fi
      continue-on-error: true

  # Linting and code quality
  lint:
    name: Code Quality
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Run TypeScript compiler check
      run: npx tsc --noEmit
      
    - name: Check for common issues
      run: |
        echo "Checking for common code issues..."
        # Check for console.log statements (should be removed in production)
        if grep -r "console\.log" src/ --include="*.ts" --include="*.js"; then
          echo "Warning: console.log statements found in source code"
        fi
        # Check for TODO/FIXME comments
        if grep -r "TODO\|FIXME" src/ --include="*.ts" --include="*.js"; then
          echo "Info: TODO/FIXME comments found"
        fi

  # Dependency vulnerability scanning
  security-audit:
    name: Security Audit
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Run npm audit
      run: |
        echo "Running npm audit..."
        npm audit --audit-level=moderate
      continue-on-error: true

    - name: Check for known vulnerabilities
      run: |
        echo "Checking for known security issues..."
        # Check for hardcoded secrets patterns
        if grep -r "password\|secret\|key\|token" src/ --include="*.ts" --include="*.js" | grep -v "process.env"; then
          echo "Warning: Potential hardcoded secrets found"
        fi
# TODO: CHECK IF THIS IS NEEDED 
  # # Docker build test (since you have Dockerfile)
  # docker-build:
  #   name: Docker Build Test
  #   runs-on: ubuntu-latest
  #   needs: build-and-test

  #   steps:
  #   - name: Checkout code
  #     uses: actions/checkout@v4

  #   - name: Build Docker image
  #     run: |
  #       echo "Testing Docker build..."
  #       docker build -t getrankt-backend:test .

  #   - name: Test Docker image
  #     run: |
  #       echo "Testing Docker image runs..."
  #       # Basic smoke test - just check if container starts
  #       docker run --rm -d --name test-container getrankt-backend:test || true
  #       sleep 5
  #       docker stop test-container || true


  deploy:
    name: Deploy to Google Cloud Run
    runs-on: ubuntu-latest
    needs: build-and-test
    if: github.ref == 'refs/heads/test-deploy'

    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Debug GCP secrets (non-sensitive)
      run: |
        echo "GCP_PROJECT_ID=${{ secrets.GCP_PROJECT_ID }}"
        echo "GCP_REGION=${{ secrets.GCP_REGION }}"
    - name: Set up Cloud SDK
      uses: google-github-actions/setup-gcloud@v1
      with:
        project_id: ${{ secrets.GCP_PROJECT_ID }}
        service_account_key: ${{ secrets.GCP_SA_KEY }}
        export_default_credentials: true

    - name: Configure Docker to use gcloud
      run: gcloud auth configure-docker

    - name: Make build script executable
      run: chmod +x ./build-api.sh

    - name: Deploy to Cloud Run using build-api.sh
      run: ./build-api.sh "${{ secrets.GCP_PROJECT_ID }}" "${{ secrets.GCP_REGION }}"
