
# Deploy api cloud run

```bash
echo "Deploying to Cloud Run:"
gcloud run deploy get-rankt-api \
    --image europe-west4-docker.pkg.dev/getrankt-8ce63/getrankt-repo/get-rankt-api:latest \
    --platform managed \
    --add-cloudsql-instances getrankt-8ce63:europe-west4:getrankt-dev \
    --vpc-connector projects/getrankt-8ce63/locations/europe-west4/connectors/getrankt-vpc-connector \
    --region europe-west4 \
    --allow-unauthenticated \
    --memory 512Mi \
    --cpu 1 \
    --update-secrets NODE_ENV=NODE_ENV:latest,HOST=HOST:latest,DB_USER=DB_USER:latest,DB_PASSWORD=DB_PASSWORD:latest,DB_HOST=DB_HOST:latest,DB_PORT=DB_PORT:latest,DB_NAME=DB_NAME:latest,DB_SCHEMA=DB_SCHEMA:latest,CONNECTION_NAME=CONNECTION_NAME:latest,JWT_SECRET=JWT_SECRET:latest,REDIS_URL=REDIS_URL:latest,REDIS_RETRY_ATTEMPTS=REDIS_RETRY_ATTEMPTS:latest,REDIS_RETRY_DELAY=REDIS_RETRY_DELAY:latest,REDIS_CONNECTION_TIMEOUT=REDIS_CONNECTION_TIMEOUT:latest
```

# update 
gcloud run services update getrankt-backend-github \
  --add-cloudsql-instances getrankt-8ce63:europe-west4:getrankt-dev \
  --update-secrets NODE_ENV=NODE_ENV:latest,HOST=HOST:latest,DB_USER=DB_USER:latest,DB_PASSWORD=DB_PASSWORD:latest,DB_HOST=DB_HOST:latest,DB_PORT=DB_PORT:latest,DB_NAME=DB_NAME:latest,DB_SCHEMA=DB_SCHEMA:latest,CONNECTION_NAME=CONNECTION_NAME:latest,JWT_SECRET=JWT_SECRET:latest,REDIS_URL=REDIS_URL:latest,REDIS_RETRY_ATTEMPTS=REDIS_RETRY_ATTEMPTS:latest,REDIS_RETRY_DELAY=REDIS_RETRY_DELAY:latest,REDIS_CONNECTION_TIMEOUT=REDIS_CONNECTION_TIMEOUT:latest
  --region europe-west4

# Create a job to setup the database

```bash
gcloud run jobs create db-setup-job \
  --image europe-west4-docker.pkg.dev/getrankt-8ce63/getrankt-repo/get-rankt-api:latest \
  --set-cloudsql-instances getrankt-8ce63:europe-west4:getrankt-development \
  --vpc-connector projects/getrankt-8ce63/locations/europe-west4/connectors/getrankt-vpc-connector \
  --update-secrets DATABASE_URL=DATABASE_URL:latest,NODE_ENV=NODE_ENV:latest,HOST=HOST:latest,DB_USER=DB_USER:latest,DB_PASSWORD=DB_PASSWORD:latest,DB_HOST=DB_HOST:latest,DB_PORT=DB_PORT:latest,DB_NAME=DB_NAME:latest,DB_SCHEMA=DB_SCHEMA:latest,CONNECTION_NAME=CONNECTION_NAME:latest,JWT_SECRET=JWT_SECRET:latest,REDIS_URL=REDIS_URL:latest,REDIS_RETRY_ATTEMPTS=REDIS_RETRY_ATTEMPTS:latest,REDIS_RETRY_DELAY=REDIS_RETRY_DELAY:latest,REDIS_CONNECTION_TIMEOUT=REDIS_CONNECTION_TIMEOUT:latest \
  --region europe-west4 \
  --command "sh" \
  --args "-c","cd /app && /usr/local/bin/node /app/node_modules/.bin/prisma db push --accept-data-loss"
```

# Execute the setup database job

```bash
gcloud run jobs execute db-setup-job --region europe-west4
```