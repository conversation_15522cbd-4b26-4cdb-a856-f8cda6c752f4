{"name": "getrankt-backend", "version": "1.0.0", "main": "app.js", "scripts": {"dev": "nodemon src/app.ts", "build": "tsc", "start": "node dist/src/app.js", "deploy": "bash gcp/build-api.sh", "redis:consumer:dev": "ts-node scripts/redisVoteConsumer.ts", "redis:consumer:prod": "node dist/scripts/redisVoteConsumer.js", "redis:producer:dev": "ts-node scripts/redisVoteProducer.ts", "redis:producer:prod": "node dist/scripts/redisVoteProducer.js", "dev:docker": "bash scripts/manage-dev.sh start", "dev:docker:stop": "bash scripts/manage-dev.sh stop", "dev:docker:restart": "bash scripts/manage-dev.sh restart", "dev:docker:logs": "bash scripts/manage-dev.sh logs", "dev:docker:logs:api": "bash scripts/manage-dev.sh logs-api", "dev:docker:logs:db": "bash scripts/manage-dev.sh logs-db", "dev:docker:status": "bash scripts/manage-dev.sh status", "dev:docker:build": "bash scripts/manage-dev.sh build", "dev:docker:db-push": "bash scripts/manage-dev.sh db-push", "dev:docker:studio": "bash scripts/manage-dev.sh studio", "dev:docker:studio:stop": "bash scripts/manage-dev.sh studio-stop", "test:data": "ts-node scripts/createTestTournaments.ts", "test:clean": "ts-node scripts/cleanupTestData.ts"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@prisma/client": "^6.12.0", "prisma": "^6.12.0", "@types/node-fetch": "^2.6.12", "@types/uuid": "^10.0.0", "bcrypt": "^6.0.0", "dotenv": "^16.5.0", "express": "^5.1.0", "express-rate-limit": "^8.0.1", "express-validator": "^7.2.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "node-fetch": "^3.3.2", "redis": "^5.6.1", "uuid": "^11.1.0"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/express": "^5.0.2", "@types/express-rate-limit": "^5.1.3", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^22.15.21", "@types/redis": "^4.0.10", "nodemon": "^3.1.10", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}