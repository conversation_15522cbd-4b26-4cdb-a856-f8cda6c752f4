# ==============================
# Build Stage
# ==============================

# Docker multi-stage builds. First builder stage
# Full dev environment to build/compile your app (TypeScript → JavaScript, Prisma client, etc.)

FROM node:18-slim AS builder

# Install build dependencies for native modules
RUN apt-get update && apt-get install -y \
    python3 \
    make \
    g++ \
    openssl \
    ca-certificates \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app

# Copy package and tsconfig files
COPY package*.json ./
COPY tsconfig.json ./

# Install all dependencies (including dev for Prisma + TypeScript build)
RUN npm ci && npm cache clean --force

# Copy source, scripts, prisma
COPY src ./src
COPY scripts ./scripts
COPY prisma ./prisma
COPY start-api.sh ./

# Generates the Prisma client based on the schema.prisma file.
RUN npx prisma generate

# Compiles TypeScript (src/) into JavaScript (dist/).
RUN npm run build


# ==============================
# Production Stage
# ==============================
# A fresh, clean runtime layer (no dev tools, just the minimal image for running the app).
# Docker multi-stage builds. Second production stage

FROM node:18-slim AS production

# Install minimal runtime dependencies
# dumb-init: acts as PID 1 to handle signals correctly (important in containers).
# Creates a non-root user (nodejs) for security.

# Clean, minimal image that contains only what's needed to run the already-built app



RUN apt-get update && apt-get install -y \
    dumb-init \
    openssl \
    && groupadd -g 1001 nodejs \
    && useradd -r -u 1001 -g nodejs nodejs \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app

#  Copy only package files for production install
COPY --from=builder /app/package*.json ./

# Installs just prod dependencies, not dev ones (like TypeScript, Prisma CLI, etc.).
# devDependencies like typescript, eslint, jest, nodemon, ts-node are required only to develop or build the app.
# not needed in prod env
RUN npm ci --only=production && npm cache clean --force

# Ensures commands like prisma are available in $PATH.
ENV PATH=/app/node_modules/.bin:$PATH

#  Copy Prisma schema & migrations
COPY --from=builder /app/prisma ./prisma

#  Prisma client copied from builder stage

#  Copy built app and scripts
COPY --from=builder /app/dist ./dist
COPY --from=builder /app/node_modules/.prisma ./node_modules/.prisma
COPY --from=builder /app/node_modules/@prisma ./node_modules/@prisma
COPY --from=builder /app/start-api.sh ./
RUN chmod +x start-api.sh

# Set ownership and switch to non-root user
RUN chown -R nodejs:nodejs /app
USER nodejs

EXPOSE 8080
ENV SERVICE_TYPE=api
ENV HOST=0.0.0.0

# Start the app via your script, using dumb-init for proper signal handling (e.g., for graceful shutdowns).

CMD ["dumb-init", "./start-api.sh"]
