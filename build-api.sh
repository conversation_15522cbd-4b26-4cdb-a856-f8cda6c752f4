#!/bin/bash

# Build script for GetRankt API Server
# Usage: ./build-api.sh [PROJECT_ID] [REGION]

# Exit immediately if error occurs
set -e

PROJECT_ID=${1:-"getrankt-8ce63"}
REGION=${2:-"europe-west4"}
REPOSITORY="getrankt-repo"
SERVICE_NAME="get-rankt-api"
# Use Artifact Registry format: REGION-docker.pkg.dev/PROJECT_ID/REPOSITORY/IMAGE
IMAGE_NAME="${REGION}-docker.pkg.dev/${PROJECT_ID}/${REPOSITORY}/${SERVICE_NAME}"

echo "🏗️  Building GetRankt API Server Docker image..."
echo "Project ID: ${PROJECT_ID}"
echo "Region: ${REGION}"
echo "Service: ${SERVICE_NAME}"
echo "Image: ${IMAGE_NAME}"

# Build the Docker image for AMD64/Linux (Cloud Run requirement)
echo "📦 Building Docker image for linux/amd64..."
docker build --platform linux/amd64 -f Dockerfile.api -t ${IMAGE_NAME}:latest .

# Tag with timestamp for versioning
TIMESTAMP=$(date +%Y%m%d-%H%M%S)
docker tag ${IMAGE_NAME}:latest ${IMAGE_NAME}:${TIMESTAMP}

echo "✅ Docker image built successfully!"
echo "   Latest: ${IMAGE_NAME}:latest"
echo "   Tagged: ${IMAGE_NAME}:${TIMESTAMP}"

echo "🚀 Pushing to Google Container Registry:"
docker push ${IMAGE_NAME}:latest


# Deploy api cloud run
echo "Deploying to Cloud Run:"
gcloud run deploy get-rankt-api \
    --image europe-west4-docker.pkg.dev/getrankt-8ce63/getrankt-repo/get-rankt-api:latest \
    --platform managed \
    --add-cloudsql-instances getrankt-8ce63:europe-west4:getrankt-dev \
    --vpc-connector projects/getrankt-8ce63/locations/europe-west4/connectors/getrankt-vpc-connector \
    --region europe-west4 \
    --allow-unauthenticated \
    --memory 512Mi \
    --cpu 1 \
    --set-secrets NODE_ENV=NODE_ENV:latest,HOST=HOST:latest,DB_USER=DB_USER:latest,DB_PASSWORD=DB_PASSWORD:latest,DB_HOST=DB_HOST:latest,DB_PORT=DB_PORT:latest,DB_NAME=DB_NAME:latest,DB_SCHEMA=DB_SCHEMA:latest,CONNECTION_NAME=CONNECTION_NAME:latest,JWT_SECRET=JWT_SECRET:latest,REDIS_URL=REDIS_URL:latest,REDIS_RETRY_ATTEMPTS=REDIS_RETRY_ATTEMPTS:latest,REDIS_RETRY_DELAY=REDIS_RETRY_DELAY:latest,REDIS_CONNECTION_TIMEOUT=REDIS_CONNECTION_TIMEOUT:latest
