# GetRankt Backend

A tournament platform API with voting-based ranking system for video submissions.

## 🎯 What is GetRankt?

GetRankt is a competitive tournament platform where users submit videos and vote on matchups to determine rankings. Features include:

- **Tournament Management**: Create and manage tournaments with entry fees
- **Video Submissions**: Users submit videos to compete in tournaments
- **Voting System**: Vote on head-to-head matchups between submissions
- **Coin Economy**: Virtual currency system for tournament entry fees
- **User Profiles**: Authentication, avatars, and user management
- **Real-time Processing**: Redis-based vote processing for scalability

## 🚀 Quick Start

### Prerequisites
- Node.js 18+
- Docker & Docker Compose
- PostgreSQL (via Docker)

### Setup
1. **Clone and install dependencies**
   ```bash
   git clone <repo-url>
   cd getrankt-backend
   npm install
   ```

2. **Start database**
   ```bash
   docker-compose up -d
   ```

3. **Configure environment**
   Create `.env` file:
   ```env
   DATABASE_URL="postgresql://admin:admin@localhost:5432/getrankt?schema=public"
   JWT_SECRET=your-secret-key
   PORT=4000
   ```

4. **Setup database**
   ```bash
   npx prisma migrate dev --name init
   ```

5. **Start development server**
   ```bash
   npm run dev
   ```

## 📚 API Documentation

- **Health Check**: `GET /health`
- **Authentication**: `POST /api/auth/signup`, `POST /api/auth/login`
- **Tournaments**: `GET /api/tournaments`, `POST /api/tournaments`
- **Submissions**: `POST /api/tournaments/:id/submit`
- **Voting**: `POST /api/tournaments/:id/vote`
- **Coins**: `GET /api/coin/balance`

Full API documentation available at `/api-docs` when running locally.

## 🛠 Tech Stack

- **Runtime**: Node.js + TypeScript
- **Framework**: Express.js
- **Database**: PostgreSQL + Prisma ORM
- **Cache/Queue**: Redis
- **Authentication**: JWT
- **Deployment**: Google Cloud Platform
- **CI/CD**: GitHub Actions

