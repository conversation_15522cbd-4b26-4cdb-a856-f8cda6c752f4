#!/usr/bin/env ts-node

/**
 * Tournament Test Data Generator
 * 
 * Creates tournaments with different states that suit the tournament logic:
 * - Tournament Creation: Creates tournaments with various maxContestants (4, 16, 32, 64, 128)
 * - Submission Logic: Creates test users and submissions with coin transactions
 * - Voting Logic: Creates votes with unique constraints per user/matchup
 * - Bracket Logic: Advances tournaments through different bracket rounds
 * - Matchup Logic: Creates active/inactive matchups for different brackets
 * - Winner Logic: Completes tournaments with winners
 */

import { PrismaClient } from '@prisma/client'
import { TOURNAMENT_STATUS, SUBMISSION_STATUS, SUPPORTED_TOURNAMENT_SIZES } from '../src/constants/tournamentConstants'
import * as dotenv from 'dotenv'

/**
 * Dynamic Environment Detection for Test Scripts
 * Automatically detects and connects to the appropriate database environment
 */
function detectEnvironment() {
  // Check command line argument for environment override
  const envArg = process.argv.find(arg => arg.startsWith('--env='))
  if (envArg) {
    const env = envArg.split('=')[1]
    console.log(`🎯 Using environment override: ${env}`)
    return env
  }

  // Auto-detect based on running Docker containers
  const { execSync } = require('child_process')

  try {
    // Check if development Docker containers are running
    // Using safer execSync with explicit shell: false and array arguments
    const devContainers = execSync('docker ps --format "{{.Names}}"', {
      encoding: 'utf8',
      timeout: 5000 // 5 second timeout
    }).trim()

    if (devContainers && (devContainers.includes('getrankt-api-dev') || devContainers.includes('getrankt-db-dev'))) {
      const devLines = devContainers.split('\n').filter((line: string) =>
        line.includes('getrankt') && line.includes('dev')
      )
      if (devLines.length > 0) {
        console.log(`🔍 Detected development Docker containers: ${devLines.join(', ')}`)
        return 'development'
      }
    }

    // Check if local production Docker containers are running
    if (devContainers && (devContainers.includes('getrankt-api-local') || devContainers.includes('getrankt-db-local'))) {
      const localLines = devContainers.split('\n').filter((line: string) =>
        line.includes('getrankt') && line.includes('local')
      )
      if (localLines.length > 0) {
        console.log(`🔍 Detected local production Docker containers: ${localLines.join(', ')}`)
        return 'local'
      }
    }

    // Default to local if no containers detected
    console.log(`🔍 No Docker containers detected, defaulting to local production`)
    return 'local'
  } catch (error) {
    console.log(`⚠️  Could not detect environment, defaulting to local production`)
    return 'local'
  }
}

// Detect environment and load appropriate configuration
const environment = detectEnvironment()
let envFile: string = '.env'
let dbConfig: any

dotenv.config({ path: envFile })
dbConfig = {
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  host: process.env.DB_HOST === 'db' ? 'localhost' : process.env.DB_HOST, // Use localhost when running outside Docker
  port: process.env.DB_EXTERNAL_PORT,
  name: process.env.DB_NAME,
  schema: process.env.DB_SCHEMA
}

const DATABASE_URL = `postgresql://${dbConfig.user}:${dbConfig.password}@${dbConfig.host}:${dbConfig.port}/${dbConfig.name}?schema=${dbConfig.schema}`

console.log(`📊 Environment: ${environment}`)
console.log(`📁 Config file: ${envFile}`)
console.log(`🗄️  Database: ${dbConfig.name} on port ${dbConfig.port}`)

// Configure Prisma to connect to database
const prisma = new PrismaClient({
  datasources: {
    db: {
      url: DATABASE_URL
    }
  }
})

interface TestUser {
  id: string
  username: string
  email: string
  coins: number
}

interface TestTournament {
  id: string
  title: string
  status: string
  maxContestants: number
  entryFee: number
}

class TournamentTestDataGenerator {
  private testUsers: TestUser[] = []
  private testTournaments: TestTournament[] = []

  async generateTestData() {
    console.log('🚀 Starting tournament test data generation...')
    console.log(`📊 Connecting to database: ${DATABASE_URL.replace(/:[^:@]*@/, ':***@')}`)

    try {
      // Step 1: Create test users with sufficient coins
      await this.createTestUsers()
      
      // Step 2: Create tournaments in different states
      await this.createOpenTournaments()
      await this.createActiveTournaments()
      await this.createCompletedTournaments()
      
      console.log('✅ Tournament test data generation completed successfully!')
      console.log(`Created ${this.testUsers.length} test users`)
      console.log(`Created ${this.testTournaments.length} test tournaments`)
      
    } catch (error) {
      console.error('❌ Error generating test data:', error)
      throw error
    }
  }

  /**
   * Create test users with sufficient coins for tournament entries
   */
  private async createTestUsers() {
    console.log('👥 Creating test users...')
    
    const userCount = 150 // Enough for largest tournament (128) + extras
    const baseCoins = 1000 // Sufficient for multiple tournament entries
    
    for (let i = 1; i <= userCount; i++) {
      const userData = {
        username: `testuser${i}`,
        email: `testuser${i}@test.com`,
        passwordHash: '$2b$10$dummy.hash.for.testing.purposes.only',
        coins: baseCoins,
        avatarUrl: `https://api.dicebear.com/7.x/avataaars/png?seed=testuser${i}`
      }
      
      const user = await prisma.user.create({ data: userData })
      this.testUsers.push({
        id: user.id,
        username: user.username,
        email: user.email,
        coins: user.coins
      })
    }
    
    console.log(`✅ Created ${userCount} test users`)
  }

  /**
   * Create tournaments in OPEN state (accepting submissions)
   */
  private async createOpenTournaments() {
    console.log('🏆 Creating OPEN tournaments...')
    
    const openTournaments = [
      { size: 4, title: 'Mini Tournament - Open', entryFee: 10 },
      { size: 16, title: 'Small Tournament - Open', entryFee: 25 },
      { size: 32, title: 'Medium Tournament - Open', entryFee: 50 },
      { size: 64, title: 'Large Tournament - Open', entryFee: 100 }
    ]
    
    for (const config of openTournaments) {
      const tournament = await this.createTournamentWithSubmissions(
        config.title,
        config.size,
        config.entryFee,
        TOURNAMENT_STATUS.OPEN,
        Math.floor(config.size * 0.7) // 70% filled
      )
      this.testTournaments.push(tournament)
    }
    
    console.log(`✅ Created ${openTournaments.length} OPEN tournaments`)
  }

  /**
   * Create tournaments in ACTIVE state (with voting and brackets)
   */
  private async createActiveTournaments() {
    console.log('⚡ Creating ACTIVE tournaments...')
    
    const activeTournaments = [
      { size: 4, title: 'Mini Tournament - Round 1', entryFee: 10, bracket: 1 },
      { size: 16, title: 'Small Tournament - Round 2', entryFee: 25, bracket: 2 },
      { size: 32, title: 'Medium Tournament - Round 3', entryFee: 50, bracket: 3 },
      { size: 64, title: 'Large Tournament - Semi Finals', entryFee: 100, bracket: 5 }
    ]
    
    for (const config of activeTournaments) {
      const tournament = await this.createActiveTournamentWithVoting(
        config.title,
        config.size,
        config.entryFee,
        config.bracket
      )
      this.testTournaments.push(tournament)
    }
    
    console.log(`✅ Created ${activeTournaments.length} ACTIVE tournaments`)
  }

  /**
   * Create tournaments in COMPLETED state (with winners)
   */
  private async createCompletedTournaments() {
    console.log('🏅 Creating COMPLETED tournaments...')
    
    const completedTournaments = [
      { size: 4, title: 'Mini Tournament - Completed', entryFee: 10 },
      { size: 16, title: 'Small Tournament - Completed', entryFee: 25 }
    ]
    
    for (const config of completedTournaments) {
      const tournament = await this.createCompletedTournamentWithWinner(
        config.title,
        config.size,
        config.entryFee
      )
      this.testTournaments.push(tournament)
    }
    
    console.log(`✅ Created ${completedTournaments.length} COMPLETED tournaments`)
  }

  /**
   * Create a tournament with submissions but not yet started
   */
  private async createTournamentWithSubmissions(
    title: string,
    maxContestants: number,
    entryFee: number,
    status: string,
    submissionCount: number
  ): Promise<TestTournament> {
    // Create tournament
    const creatorUser = this.testUsers[0]
    const tournament = await prisma.tournament.create({
      data: {
        title,
        category: 'Test Category',
        createdById: creatorUser.id,
        entryFee,
        maxContestants,
        status
      }
    })
    
    // Add submissions with coin transactions
    const submitters = this.testUsers.slice(1, submissionCount + 1)
    for (const user of submitters) {
      await this.createSubmissionWithTransaction(tournament.id, user.id, entryFee)
    }
    
    return {
      id: tournament.id,
      title: tournament.title,
      status: tournament.status,
      maxContestants: tournament.maxContestants,
      entryFee: tournament.entryFee
    }
  }

  /**
   * Create an active tournament with matchups and votes
   */
  private async createActiveTournamentWithVoting(
    title: string,
    maxContestants: number,
    entryFee: number,
    currentBracket: number
  ): Promise<TestTournament> {
    // Create tournament
    const creatorUser = this.testUsers[0]
    const tournament = await prisma.tournament.create({
      data: {
        title,
        category: 'Test Category',
        createdById: creatorUser.id,
        entryFee,
        maxContestants,
        status: TOURNAMENT_STATUS.ACTIVE,
        currentBracket
      }
    })
    
    // Add full submissions
    const submitters = this.testUsers.slice(1, maxContestants + 1)
    const submissions = []
    for (const user of submitters) {
      const submission = await this.createSubmissionWithTransaction(tournament.id, user.id, entryFee)
      submissions.push(submission)
    }
    
    // Create matchups for current bracket
    await this.createMatchupsForBracket(tournament.id, submissions, currentBracket)
    
    // Add some votes
    await this.createVotesForTournament(tournament.id, currentBracket)
    
    return {
      id: tournament.id,
      title: tournament.title,
      status: tournament.status,
      maxContestants: tournament.maxContestants,
      entryFee: tournament.entryFee
    }
  }

  /**
   * Create a completed tournament with full bracket history and winner
   */
  private async createCompletedTournamentWithWinner(
    title: string,
    maxContestants: number,
    entryFee: number
  ): Promise<TestTournament> {
    // Create tournament
    const creatorUser = this.testUsers[0]
    const tournament = await prisma.tournament.create({
      data: {
        title,
        category: 'Test Category',
        createdById: creatorUser.id,
        entryFee,
        maxContestants,
        status: TOURNAMENT_STATUS.COMPLETED,
        currentBracket: Math.log2(maxContestants) // Set to final round
      }
    })

    // Add submissions
    const submitters = this.testUsers.slice(1, maxContestants + 1)
    const submissions = []
    for (const user of submitters) {
      const submission = await this.createSubmissionWithTransaction(
        tournament.id,
        user.id,
        entryFee,
        SUBMISSION_STATUS.ACTIVE // Start as active, will be updated during simulation
      )
      submissions.push(submission)
    }

    // Simulate full tournament progression
    await this.simulateCompleteTournament(tournament.id, submissions, maxContestants)

    return {
      id: tournament.id,
      title: tournament.title,
      status: tournament.status,
      maxContestants: tournament.maxContestants,
      entryFee: tournament.entryFee
    }
  }

  /**
   * Create submission with coin transaction
   */
  private async createSubmissionWithTransaction(
    tournamentId: string,
    userId: string,
    entryFee: number,
    status: string = SUBMISSION_STATUS.ACTIVE
  ) {
    return await prisma.$transaction(async (tx: any) => {
      // Create coin transaction
      await tx.coinTransaction.create({
        data: {
          userId,
          type: 'TournamentEntry',
          amount: -entryFee,
          relatedTournamentId: tournamentId
        }
      })
      
      // Update user coins
      await tx.user.update({
        where: { id: userId },
        data: { coins: { decrement: entryFee } }
      })
      
      // Create submission
      const submission = await tx.submission.create({
        data: {
          userId,
          tournamentId,
          videoUrl: `https://example.com/video/${userId}`,
          status
        }
      })
      
      return submission
    })
  }

  /**
   * Create matchups for a bracket
   */
  private async createMatchupsForBracket(
    tournamentId: string,
    submissions: any[],
    bracketRound: number
  ) {
    // Shuffle submissions for random matchups
    const shuffled = [...submissions].sort(() => Math.random() - 0.5)
    
    // Create matchups in pairs
    for (let i = 0; i < shuffled.length; i += 2) {
      if (i + 1 < shuffled.length) {
        await prisma.matchup.create({
          data: {
            tournamentId,
            firstSubmissionId: shuffled[i].id,
            secondSubmissionId: shuffled[i + 1].id,
            bracketRound,
            isActive: true
          }
        })
      }
    }
  }

  /**
   * Simulate complete tournament progression with all brackets, matchups, and votes
   */
  private async simulateCompleteTournament(
    tournamentId: string,
    submissions: any[],
    maxContestants: number
  ) {
    const totalRounds = Math.log2(maxContestants)
    let currentSubmissions = [...submissions]

    // Simulate each bracket round
    for (let round = 1; round <= totalRounds; round++) {
      console.log(`   Simulating round ${round}/${totalRounds} (${currentSubmissions.length} contestants)`)

      // Create matchups for this round
      const matchups = await this.createMatchupsForRound(tournamentId, currentSubmissions, round)

      // Create votes for each matchup and determine winners
      const roundWinners = []
      for (const matchup of matchups) {
        const winner = await this.simulateMatchupVoting(tournamentId, matchup, round)
        roundWinners.push(winner)
      }

      // Update submissions for next round or mark as eliminated
      if (round < totalRounds) {
        // Advance winners, eliminate losers
        for (const submission of currentSubmissions) {
          if (roundWinners.find(w => w.id === submission.id)) {
            // Winner advances
            await prisma.submission.update({
              where: { id: submission.id },
              data: { currentBracket: round + 1 }
            })
          } else {
            // Loser is eliminated
            await prisma.submission.update({
              where: { id: submission.id },
              data: {
                status: SUBMISSION_STATUS.ELIMINATED,
                eliminatedAt: new Date()
              }
            })
          }
        }
        currentSubmissions = roundWinners
      } else {
        // Final round - mark winner and eliminate runner-up
        const winner = roundWinners[0]
        await prisma.submission.update({
          where: { id: winner.id },
          data: { status: SUBMISSION_STATUS.WINNER }
        })

        // Mark all others as eliminated
        for (const submission of currentSubmissions) {
          if (submission.id !== winner.id) {
            await prisma.submission.update({
              where: { id: submission.id },
              data: {
                status: SUBMISSION_STATUS.ELIMINATED,
                eliminatedAt: new Date()
              }
            })
          }
        }
      }
    }
  }

  /**
   * Create matchups for a specific round
   */
  private async createMatchupsForRound(
    tournamentId: string,
    submissions: any[],
    bracketRound: number
  ) {
    const matchups = []

    // Shuffle submissions for random matchups
    const shuffled = [...submissions].sort(() => Math.random() - 0.5)

    // Create matchups in pairs
    for (let i = 0; i < shuffled.length; i += 2) {
      if (i + 1 < shuffled.length) {
        const matchup = await prisma.matchup.create({
          data: {
            tournamentId,
            firstSubmissionId: shuffled[i].id,
            secondSubmissionId: shuffled[i + 1].id,
            bracketRound,
            isActive: false // Mark as inactive since tournament is completed
          }
        })
        matchups.push({
          ...matchup,
          firstSubmission: shuffled[i],
          secondSubmission: shuffled[i + 1]
        })
      }
    }

    return matchups
  }

  /**
   * Simulate voting for a matchup and return the winner
   */
  private async simulateMatchupVoting(
    tournamentId: string,
    matchup: any,
    bracketRound: number
  ) {
    // Get voters (users not in this tournament)
    const tournamentSubmissions = await prisma.submission.findMany({
      where: { tournamentId },
      select: { userId: true }
    })
    const contestantIds = tournamentSubmissions.map((s: any) => s.userId)
    const voters = this.testUsers.filter(u => !contestantIds.includes(u.id))

    // Create 3-7 votes for this matchup
    const voteCount = Math.floor(Math.random() * 5) + 3
    const selectedVoters = voters.slice(0, Math.min(voteCount, voters.length))

    let firstSubmissionVotes = 0
    let secondSubmissionVotes = 0

    for (const voter of selectedVoters) {
      // Randomly select winner (slightly favor first submission for deterministic results)
      const winnerSubmissionId = Math.random() > 0.4
        ? matchup.firstSubmissionId
        : matchup.secondSubmissionId

      if (winnerSubmissionId === matchup.firstSubmissionId) {
        firstSubmissionVotes++
      } else {
        secondSubmissionVotes++
      }

      // Create vote and update winner's vote count
      await prisma.$transaction(async (tx: any) => {
        await tx.vote.create({
          data: {
            voterId: voter.id,
            tournamentId,
            firstSubmissionId: matchup.firstSubmissionId,
            secondSubmissionId: matchup.secondSubmissionId,
            winnerSubmissionId,
            bracketRound
          }
        })

        await tx.submission.update({
          where: { id: winnerSubmissionId },
          data: { votes: { increment: 1 } }
        })
      })
    }

    // Return the submission with more votes
    return firstSubmissionVotes >= secondSubmissionVotes
      ? matchup.firstSubmission
      : matchup.secondSubmission
  }

  /**
   * Create votes for tournament matchups
   */
  private async createVotesForTournament(tournamentId: string, bracketRound: number) {
    // Get active matchups
    const matchups = await prisma.matchup.findMany({
      where: { tournamentId, bracketRound, isActive: true },
      include: {
        firstSubmission: true,
        secondSubmission: true
      }
    })
    
    // Get voters (users not in this tournament)
    const tournamentSubmissions = await prisma.submission.findMany({
      where: { tournamentId },
      select: { userId: true }
    })
    const contestantIds = tournamentSubmissions.map((s: any) => s.userId)
    const voters = this.testUsers.filter(u => !contestantIds.includes(u.id))
    
    // Create votes for each matchup
    for (const matchup of matchups) {
      const voteCount = Math.floor(Math.random() * 5) + 1 // 1-5 votes per matchup
      const selectedVoters = voters.slice(0, voteCount)
      
      for (const voter of selectedVoters) {
        // Randomly select winner
        const winnerSubmissionId = Math.random() > 0.5 
          ? matchup.firstSubmissionId 
          : matchup.secondSubmissionId
        
        // Create vote and update winner's vote count
        await prisma.$transaction(async (tx: any) => {
          await tx.vote.create({
            data: {
              voterId: voter.id,
              tournamentId,
              firstSubmissionId: matchup.firstSubmissionId,
              secondSubmissionId: matchup.secondSubmissionId,
              winnerSubmissionId,
              bracketRound
            }
          })
          
          await tx.submission.update({
            where: { id: winnerSubmissionId },
            data: { votes: { increment: 1 } }
          })
        })
      }
    }
  }
}

// Main execution
async function main() {
  const generator = new TournamentTestDataGenerator()
  await generator.generateTestData()
  await prisma.$disconnect()
}

if (require.main === module) {
  main().catch((error) => {
    console.error('❌ Script failed:', error)
    process.exit(1)
  })
}

export { TournamentTestDataGenerator }
