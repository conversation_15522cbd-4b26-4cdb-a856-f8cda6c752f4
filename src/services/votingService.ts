import {
  SUBMISSION_STATUS,
  TOURNAMENT_STATUS,
  VOTES_TO_ADVANCE
} from '../constants/tournamentConstants'
import { DatabaseOperations } from '../database/DatabaseOperations'
import { VoteProducer, VoteSubmissionRequest, VoteSubmissionResponse } from '../redis/VoteProducer'
import { NotFoundError } from '../middlewares/errorMiddleware'
import { checkAndAdvanceContestants, deactivateMatchupsForQualifiedContestants } from './singleEliminationService'
import { VoteResult } from '../redis/RedisVotingStream'



interface MatchupInfo {
  id: string
  firstSubmissionId: string
  secondSubmissionId: string
  bracketRound: number
  firstSubmission: {
    id: string
    userId: string
    videoUrl: string
    votes: number
    user: {
      username: string
      avatarUrl: string | null
    }
  }
  secondSubmission: {
    id: string
    userId: string
    videoUrl: string
    votes: number
    user: {
      username: string
      avatarUrl: string | null
    }
  }
  tournament: {
    id: string
    title: string
    category: string
    status: string
  }
}

/**
 * Check if user has already voted on a specific matchup
 */
export const hasUserVotedOnMatchup = async (
  voterId: string,
  firstSubmissionId: string,
  secondSubmissionId: string
): Promise<boolean> => {
  return await DatabaseOperations.hasUserVotedOnMatchup(voterId, firstSubmissionId, secondSubmissionId)
}


/**
 * Submit a vote for a matchup
 */
export const submitVoteDB = async (
  voterId: string,
  tournamentId: string,
  firstSubmissionId: string,
  secondSubmissionId: string,
  winnerSubmissionId: string
): Promise<VoteResult> => {
  // Validate inputs
  if (winnerSubmissionId !== firstSubmissionId && winnerSubmissionId !== secondSubmissionId) {
    throw new Error('Winner must be one of the two contestants in the matchup')
  }

  // Check if user already voted on this matchup
  const alreadyVoted = await hasUserVotedOnMatchup(voterId, firstSubmissionId, secondSubmissionId)
  if (alreadyVoted) {
    throw new Error('You have already voted on this matchup')
  }

  // Get tournament and verify it's active
  const tournament = await DatabaseOperations.findTournamentById(tournamentId)

  if (!tournament) {
    throw new Error('Tournament not found')
  }

  if (tournament.status !== TOURNAMENT_STATUS.ACTIVE) {
    throw new Error('Tournament is not currently active for voting')
  }

  // Get both submissions to verify they exist and are active
  const [firstSubmission, secondSubmission] = await Promise.all([
    DatabaseOperations.findSubmissionByIdWithUser(firstSubmissionId),
    DatabaseOperations.findSubmissionByIdWithUser(secondSubmissionId)
  ])

  if (!firstSubmission || !secondSubmission) {
    throw new Error('One or both contestants not found')
  }

  if (firstSubmission.tournamentId !== tournamentId || secondSubmission.tournamentId !== tournamentId) {
    throw new Error('Contestants must be from the specified tournament')
  }

  if ((firstSubmission as any).status !== SUBMISSION_STATUS.ACTIVE || (secondSubmission as any).status !== SUBMISSION_STATUS.ACTIVE) {
    throw new Error('One or both contestants are no longer active in the tournament')
  }

  // Verify contestants are in the same bracket
  if ((firstSubmission as any).currentBracket !== (secondSubmission as any).currentBracket) {
    throw new Error('Contestants must be in the same bracket')
  }

  // Check if user is trying to vote on their own submission
  if (firstSubmission.userId === voterId || secondSubmission.userId === voterId) {
    throw new Error('You cannot vote on matchups involving your own submission')
  }

  let advancementTriggered = false
  let tournamentCompleted = false
  let winnerVotes = 0
  let loserVotes = 0

  // Process the vote in a transaction
  const voteResult = await DatabaseOperations.createVoteWithUpdate(
    voterId,
    tournamentId,
    firstSubmissionId,
    secondSubmissionId,
    winnerSubmissionId,
    (firstSubmission as any).currentBracket
  )

  winnerVotes = voteResult.updatedWinner.votes
  loserVotes = voteResult.updatedLoser?.votes || 0

  // Check if any contestants should advance (outside transaction to avoid deadlocks)
  // Calculate required votes for the current bracket
  const currentBracket = (firstSubmission as any).currentBracket
  const requiredVotes = VOTES_TO_ADVANCE * currentBracket

  if (winnerVotes >= requiredVotes) {
    // Deactivate matchups involving contestants who have reached promotion threshold
    const deactivatedCount = await deactivateMatchupsForQualifiedContestants(tournamentId, currentBracket)
    if (deactivatedCount > 0) {
      console.log(`🚫 Deactivated ${deactivatedCount} matchups involving qualified contestants`)
    }

    const advancementResult = await checkAndAdvanceContestants(tournamentId)
    advancementTriggered = advancementResult.advancedContestants.length > 0
    tournamentCompleted = advancementResult.tournamentCompleted
  }

  return {
    success: true,
    message: `Vote recorded successfully. ${firstSubmission.user.username} vs ${secondSubmission.user.username}`,
    winnerVotes,
    loserVotes,
    advancementTriggered,
    tournamentCompleted
  }
}

/**
 * Submit a vote for a matchup using Redis streams (recommended for high concurrency)
 */
export const submitVoteRedis = async (
  voterId: string,
  tournamentId: string,
  firstSubmissionId: string,
  secondSubmissionId: string,
  winnerSubmissionId: string
): Promise<VoteSubmissionResponse> => {
  const voteRequest: VoteSubmissionRequest = {
    voterId,
    tournamentId,
    firstSubmissionId,
    secondSubmissionId,
    winnerSubmissionId
  }

  return await VoteProducer.submitVoteRequest(voteRequest)
}

/**
 * Get a random matchup from any active tournament
 */
export const getRandomMatchup = async (userId: string): Promise<MatchupInfo | null> => {
  // Get all active tournaments
  const tournaments = await DatabaseOperations.findTournamentsWithActiveMatchups([TOURNAMENT_STATUS.ACTIVE])

  // Filter tournaments and matchups where user hasn't voted and isn't a contestant
  const eligibleMatchups: any[] = []

  for (const tournament of tournaments) {
    for (const matchup of (tournament as any).matchups) {
      // Skip if user is a contestant in this matchup
      if (matchup.firstSubmission.userId === userId || matchup.secondSubmission.userId === userId) {
        continue
      }

      // Skip if user already voted on this matchup
      const hasVoted = await hasUserVotedOnMatchup(
        userId,
        matchup.firstSubmissionId,
        matchup.secondSubmissionId
      )
      if (hasVoted) {
        continue
      }

      eligibleMatchups.push({
        ...matchup,
        tournament: {
          id: tournament.id,
          title: tournament.title,
          category: tournament.category,
          status: tournament.status
        }
      })
    }
  }

  if (eligibleMatchups.length === 0) {
    return null
  }

  // Return a random eligible matchup
  const randomIndex = Math.floor(Math.random() * eligibleMatchups.length)
  const selectedMatchup = eligibleMatchups[randomIndex]

  return {
    id: selectedMatchup.id,
    firstSubmissionId: selectedMatchup.firstSubmissionId,
    secondSubmissionId: selectedMatchup.secondSubmissionId,
    bracketRound: selectedMatchup.bracketRound,
    firstSubmission: {
      id: selectedMatchup.firstSubmission.id,
      userId: selectedMatchup.firstSubmission.userId,
      videoUrl: selectedMatchup.firstSubmission.videoUrl,
      votes: selectedMatchup.firstSubmission.votes,
      user: selectedMatchup.firstSubmission.user
    },
    secondSubmission: {
      id: selectedMatchup.secondSubmission.id,
      userId: selectedMatchup.secondSubmission.userId,
      videoUrl: selectedMatchup.secondSubmission.videoUrl,
      votes: selectedMatchup.secondSubmission.votes,
      user: selectedMatchup.secondSubmission.user
    },
    tournament: selectedMatchup.tournament
  }
}

/**
 * Get a random matchup from a specific tournament
 */
export const getTournamentRandomMatchup = async (tournamentId: string, userId: string): Promise<MatchupInfo | null> => {
  // Get the specific tournament with active matchups
  const tournament = await DatabaseOperations.findTournamentById(tournamentId, {
    matchups: {
      where: {
        isActive: true
      } as any,
      include: {
        firstSubmission: {
          include: {
            user: {
              select: {
                username: true,
                avatarUrl: true
              }
            }
          }
        },
        secondSubmission: {
          include: {
            user: {
              select: {
                username: true,
                avatarUrl: true
              }
            }
          }
        }
      }
    }
  })

  if (!tournament) {
    throw new NotFoundError('Tournament not found')
  }

  // Check if tournament is active
  if (tournament.status !== TOURNAMENT_STATUS.ACTIVE) {
    return null
  }

  // Filter matchups where user hasn't voted and isn't a contestant
  const eligibleMatchups: any[] = []

  for (const matchup of (tournament as any).matchups) {
    // Skip if user is a contestant in this matchup
    if (matchup.firstSubmission.userId === userId || matchup.secondSubmission.userId === userId) {
      continue
    }

    // Skip if user already voted on this matchup
    const hasVoted = await hasUserVotedOnMatchup(
      userId,
      matchup.firstSubmissionId,
      matchup.secondSubmissionId
    )
    if (hasVoted) {
      continue
    }

    eligibleMatchups.push({
      ...matchup,
      tournament: {
        id: tournament.id,
        title: tournament.title,
        category: tournament.category,
        status: tournament.status
      }
    })
  }

  if (eligibleMatchups.length === 0) {
    return null
  }

  // Return a random eligible matchup
  const randomIndex = Math.floor(Math.random() * eligibleMatchups.length)
  const selectedMatchup = eligibleMatchups[randomIndex]

  return {
    id: selectedMatchup.id,
    firstSubmissionId: selectedMatchup.firstSubmissionId,
    secondSubmissionId: selectedMatchup.secondSubmissionId,
    bracketRound: selectedMatchup.bracketRound,
    firstSubmission: {
      id: selectedMatchup.firstSubmission.id,
      userId: selectedMatchup.firstSubmission.userId,
      videoUrl: selectedMatchup.firstSubmission.videoUrl,
      votes: selectedMatchup.firstSubmission.votes,
      user: selectedMatchup.firstSubmission.user
    },
    secondSubmission: {
      id: selectedMatchup.secondSubmission.id,
      userId: selectedMatchup.secondSubmission.userId,
      videoUrl: selectedMatchup.secondSubmission.videoUrl,
      votes: selectedMatchup.secondSubmission.votes,
      user: selectedMatchup.secondSubmission.user
    },
    tournament: selectedMatchup.tournament
  }
}

/**
 * Get matchups for a specific tournament
 */
export const getTournamentMatchups = async (
  tournamentId: string,
  userId?: string
): Promise<MatchupInfo[]> => {
  const tournament = await DatabaseOperations.findTournamentById(tournamentId, {
    matchups: {
      where: {
        isActive: true
      } as any,
      include: {
        firstSubmission: {
          include: {
            user: {
              select: {
                username: true,
                avatarUrl: true
              }
            }
          }
        },
        secondSubmission: {
          include: {
            user: {
              select: {
                username: true,
                avatarUrl: true
              }
            }
          }
        }
      }
    }
  })

  if (!tournament) {
    throw new NotFoundError('Tournament not found')
  }

  const matchups = []

  for (const matchup of (tournament as any).matchups) {
    // If userId provided, filter out matchups where user is contestant or already voted
    if (userId) {
      if (matchup.firstSubmission.userId === userId || matchup.secondSubmission.userId === userId) {
        continue
      }

      const hasVoted = await hasUserVotedOnMatchup(
        userId,
        matchup.firstSubmissionId,
        matchup.secondSubmissionId
      )
      if (hasVoted) {
        continue
      }
    }

    matchups.push({
      id: matchup.id,
      firstSubmissionId: matchup.firstSubmissionId,
      secondSubmissionId: matchup.secondSubmissionId,
      bracketRound: (matchup as any).bracketRound,
      firstSubmission: {
        id: matchup.firstSubmission.id,
        userId: matchup.firstSubmission.userId,
        videoUrl: matchup.firstSubmission.videoUrl,
        votes: matchup.firstSubmission.votes,
        user: matchup.firstSubmission.user
      },
      secondSubmission: {
        id: matchup.secondSubmission.id,
        userId: matchup.secondSubmission.userId,
        videoUrl: matchup.secondSubmission.videoUrl,
        votes: matchup.secondSubmission.votes,
        user: matchup.secondSubmission.user
      },
      tournament: {
        id: tournament.id,
        title: tournament.title,
        category: tournament.category,
        status: tournament.status
      }
    })
  }

  return matchups
}
