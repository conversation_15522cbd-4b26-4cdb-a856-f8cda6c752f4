import express, { Express } from 'express'
import { Server } from 'http'

// Configuration classes
import { MiddlewareConfig } from '../config/MiddlewareConfig'
import { ServerInitializer } from '../config/ServerInitializer'
import { RouteConfig } from '../config/RouteConfig'

// Services
import { HealthCheckService } from '../services/HealthCheckService'

/**
 * AppServer - Main application server class
 * 
 * Encapsulates the Express application setup, configuration,
 * and lifecycle management in a clean, organized class structure.
 */
export class AppServer {
  private app: Express
  private server: Server | null = null
  private readonly port: number

  constructor(port: number) {
    this.app = express()
    this.port = port
    this.setupApplication()
  }

  /**
   * Setup the Express application with all middleware and routes
   */
  private setupApplication(): void {
    // Configure middleware
    MiddlewareConfig.configureAll(this.app)
    
    // Register routes
    RouteConfig.registerAllRoutes(this.app)
    
    // Register health check endpoints
    HealthCheckService.registerEndpoints(this.app)
    
    // Configure error handling (must be last)
    MiddlewareConfig.configureErrorHandling(this.app)
  }

  /**
   * Start the server with proper initialization sequence
   */
  async start(): Promise<void> {
    try {
      // Initialize all system components
      await ServerInitializer.initializeAll()
      
      // Setup graceful shutdown handlers
      ServerInitializer.setupGracefulShutdown()
      
      // Start the HTTP server
      this.server = this.app.listen(this.port, () => {
        this.displayStartupInfo()
      })

      // Handle server errors
      this.server.on('error', (error: any) => {
        if (error.code === 'EADDRINUSE') {
          console.error(`💥 Port ${this.port} is already in use`)
        } else {
          console.error('💥 Server error:', error)
        }
        process.exit(1)
      })

    } catch (error) {
      console.error('💥 Failed to start server:', error)
      process.exit(1)
    }
  }

  /**
   * Stop the server gracefully
   */
  async stop(): Promise<void> {
    if (this.server) {
      console.log('🔄 Stopping HTTP server...')
      
      return new Promise((resolve, reject) => {
        this.server!.close((error) => {
          if (error) {
            console.error('❌ Error stopping server:', error)
            reject(error)
          } else {
            console.log('✅ HTTP server stopped')
            resolve()
          }
        })
      })
    }
  }

  /**
   * Display startup information
   */
  private displayStartupInfo(): void {
    const environment = process.env.NODE_ENV || 'development'

    console.log('\n🚀 GetRankt API Server Started')
    console.log('================================')
    console.log(`📍 Server URL: http://${process.env.HOST}:${this.port}`)
    console.log(`🌍 Environment: ${environment}`)
  }

  /**
   * Get the Express application instance
   */
  getApp(): Express {
    return this.app
  }

  /**
   * Get the HTTP server instance
   */
  getServer(): Server | null {
    return this.server
  }

  /**
   * Get the server port
   */
  getPort(): number {
    return this.port
  }
}
