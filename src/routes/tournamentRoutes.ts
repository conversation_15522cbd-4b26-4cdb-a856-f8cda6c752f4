import * as express from 'express'
import {
  createTournament,
  listTournaments,
  submitToTournament,
  getSubmissionsForTournament,
  getUserSubmissionsController,
  getTournament,
  updateTournament,
  removeTournament,
  // Single-elimination tournament imports
  getTournamentMatchups,
  voteInTournamentDB,
  // Random matchup import
  getRandomMatchupController,
  getTournamentRandomMatchupController,
  // Tournament configuration import
  getSupportedTournamentSizes,
  // Tournament bracket information import
  getTournamentBracket,
} from '../controllers/tournamentController'
import { requireAuth } from '../middlewares/authMiddleware'
import { validateTournamentOwnership } from '../middlewares/ownershipMiddleware'

const router = express.Router()

// User tournament related routes
router.get('/userSubmissions', requireAuth, getUserSubmissionsController)

// Standard tournament routes
router.post('/', requireAuth, createTournament)
router.get('/', listTournaments)
router.get('/sizes', getSupportedTournamentSizes) // Get supported tournament sizes
router.get('/:id', getTournament)
router.put('/:id', requireAuth, validateTournamentOwnership, updateTournament)
router.delete('/:id', requireAuth, validateTournamentOwnership, removeTournament)
router.post('/:id/submit', requireAuth, submitToTournament)
router.get('/:id/submissions', getSubmissionsForTournament)


// Random matchup route
router.get('/random/matchup', requireAuth, getRandomMatchupController)

// Single-elimination tournament voting routes
router.get('/:id/matchups', requireAuth, getTournamentMatchups)
router.get('/:id/random-matchup', requireAuth, getTournamentRandomMatchupController)
router.post('/:id/vote', requireAuth, voteInTournamentDB)
router.get('/:id/bracket', requireAuth, getTournamentBracket) // Get tournament bracket information (public endpoint)

export default router
