import * as jwt from 'jsonwebtoken'
import * as bcrypt from 'bcrypt'

// Enhanced security configuration
console.log('🔍 Debug: Available environment variables:', Object.keys(process.env).filter(key => key.includes('JWT') || key.includes('SECRET')))
console.log('🔍 Debug: JWT_SECRET value:', process.env.JWT_SECRET ? '[PRESENT]' : '[MISSING]')
console.log('🔍 Debug: All env vars starting with J:', Object.keys(process.env).filter(key => key.startsWith('J')))

// Wait a moment for secrets to be available (Cloud Run sometimes needs this)
const getJWTSecret = () => {
  const secret = process.env.JWT_SECRET
  if (secret) {
    console.log('✅ JWT_SECRET found')
    return secret
  }

  console.error('❌ JWT_SECRET environment variable is missing')
  console.error('Available env vars:', Object.keys(process.env).sort())
  throw new Error('JWT_SECRET environment variable is required')
}

const JWT_SECRET = getJWTSecret()
const JWT_ALGORITHM = 'HS256'
const JWT_EXPIRY = '24h'  // Reduced from 7d for better security
const BCRYPT_ROUNDS = 12  // Consider increasing from 10 to 12 for better security

export const hashPassword = (plain: string) => bcrypt.hash(plain, BCRYPT_ROUNDS)
export const comparePassword = (plain: string, hashed: string) => bcrypt.compare(plain, hashed)

export const generateToken = (userId: string) => {
  return jwt.sign({ userId }, JWT_SECRET, { 
    expiresIn: JWT_EXPIRY,
    algorithm: JWT_ALGORITHM
  })
}

export const verifyToken = (token: string) => {
  return jwt.verify(token, JWT_SECRET, {
    algorithms: [JWT_ALGORITHM]
  })
}
