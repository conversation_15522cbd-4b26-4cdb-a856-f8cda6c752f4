# Dependencies
node_modules
npm-debug.log*

# Build outputs
dist

# Environment files
.env*
!.env.example

# Development files
.git
.gitignore
README.md
.vscode
.idea

# Docker files
docker-compose*.yml
Dockerfile*

# Test files
*.test.js
*.test.ts
test/
tests/
__tests__/

# Logs
logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
.nyc_output

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env*

# Mac
.DS_Store

# Windows
Thumbs.db

# Scripts that are not needed in production
scripts/manage-*.sh
